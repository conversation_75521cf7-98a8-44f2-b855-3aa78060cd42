{"name": "speer-note-app", "version": "1.0.0", "description": "An application built for  note takers", "main": "src/app.js", "directories": {"test": "test", "lib": "src"}, "dependencies": {"accepts": "^1.3.8", "array-flatten": "^1.1.1", "assert": "^2.1.0", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "bytes": "^3.1.2", "call-bind": "^1.0.2", "chai": "^5.0.0", "chai-http": "^4.4.0", "content-disposition": "^0.5.4", "content-type": "^1.0.5", "cookie": "^0.5.0", "cookie-signature": "^1.0.6", "cors": "^2.8.5", "debug": "^2.6.9", "denque": "^2.1.0", "depd": "^2.0.0", "destroy": "^1.2.0", "dotenv": "^16.3.1", "dottie": "^2.0.6", "ee-first": "^1.1.1", "encodeurl": "^1.0.2", "escape-html": "^1.0.3", "etag": "^1.8.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "finalhandler": "^1.2.0", "forwarded": "^0.2.0", "fresh": "^0.5.2", "function-bind": "^1.1.2", "generate-function": "^2.3.1", "get-intrinsic": "^1.2.1", "has": "^1.0.4", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "http-errors": "^2.0.0", "iconv-lite": "^0.4.24", "inflection": "^1.13.4", "inherits": "^2.0.4", "ipaddr.js": "^1.9.1", "is-property": "^1.0.2", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "long": "^5.2.3", "lru-cache": "^8.0.5", "media-typer": "^0.3.0", "merge-descriptors": "^1.0.1", "methods": "^1.1.2", "mime": "^1.6.0", "mime-db": "^1.52.0", "mime-types": "^2.1.35", "mocha": "^10.2.0", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "ms": "^2.0.0", "mysql2": "^3.6.2", "named-placeholders": "^1.1.3", "negotiator": "^0.6.3", "object-inspect": "^1.13.0", "on-finished": "^2.4.1", "parseurl": "^1.3.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "path-to-regexp": "^0.1.7", "pg": "^8.11.3", "pg-connection-string": "^2.6.2", "proxy-addr": "^2.0.7", "qs": "^6.11.0", "range-parser": "^1.2.1", "raw-body": "^2.5.2", "retry-as-promised": "^7.0.4", "safe-buffer": "^5.2.1", "safer-buffer": "^2.1.2", "semver": "^7.5.4", "send": "^0.18.0", "seq-queue": "^0.0.5", "sequelize": "^6.35.2", "sequelize-auto": "^0.8.8", "sequelize-cli": "^6.6.2", "sequelize-pool": "^7.1.0", "serve-static": "^1.15.0", "setprototypeof": "^1.2.0", "side-channel": "^1.0.4", "sqlstring": "^2.3.3", "statuses": "^2.0.1", "supertest": "^6.3.3", "toidentifier": "^1.0.1", "toposort-class": "^1.0.1", "type-is": "^1.6.18", "undici-types": "^5.25.3", "unpipe": "^1.0.0", "utils-merge": "^1.0.1", "uuid": "^8.3.2", "validator": "^13.11.0", "vary": "^1.1.2", "wkx": "^0.5.0", "yallist": "^4.0.0"}, "scripts": {"start": "node src/app.js", "test": "mocha --require @babel/register 'test/**/*.js'"}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"@babel/core": "^7.23.7", "@babel/preset-env": "^7.23.7", "@babel/register": "^7.23.7"}}